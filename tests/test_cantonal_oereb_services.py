#!/usr/bin/env python3
"""
Comprehensive test script for cantonal ÖREB services.

This script tests all Swiss cantonal ÖREB services to determine which ones are working
and which ones are not. It tests XML, PDF, JSON, and URL endpoints for each canton.

Usage:
    python tests/test_cantonal_oereb_services.py
"""

import asyncio
import httpx
import sys
from typing import Dict, List
from app.config.oereb_config import CANTONAL_OEREB_SERVICES, SPECIAL_ENDPOINTS
from app.config.known_egrids import KNOWN_WORKING_EGRIDS


async def test_cantonal_oereb_services():
    """Test all cantonal ÖREB services with specific coordinates."""

    # Test coordinates for each canton
    TEST_COORDINATES = {
        'AR': (2739619.0, 1246840.0),
        'AI': (2748581.0, 1244163.0),
        'AG': (2662213.0, 1244690.0),
        'BL': (2627769.3, 1255837.6),
        'BS': (2611458.0, 1265889.0),
        'FR': (2578108.0, 1183210.0),
        'GE': (2499892.8, 1117760.5),
        'GL': (2723098.0, 1219208.0),
        'SG': (2723649.1, 1255918.7),
        'UR': (2691354.0, 1192598.3),
        'GR': (2759327.7, 1191013.8),
        'LU': (2666000.0, 1212100.0),
        'OW': (2661487.7, 1194043.6),
        'NW': (2672930.1, 1195842.5),
        'VD': (2532449.2, 1153389.3),
        'NE': (2557201.0, 1203104.0),
        'JU': (2572882.0, 1249882.0),
        'TI': (2707314.9, 1115261.4),
        'ZG': (2680990.0, 1225288.0),
        'ZH': (2682302.4, 1247858.0),
        'BE': (2600000.0, 1200000.0),  # Approximate Bern coordinates
        'TG': (2720000.0, 1270000.0),  # Approximate Thurgau coordinates
        'SO': (2610000.0, 1230000.0),  # Approximate Solothurn coordinates
        'SH': (2690000.0, 1290000.0),  # Approximate Schaffhausen coordinates
        'VS': (2600000.0, 1120000.0),  # Approximate Valais coordinates
        'SZ': (2690000.0, 1210000.0),  # Approximate Schwyz coordinates
    }

    # Known working EGRIDs are imported from app.config.known_egrids



    print(f"{'='*80}")
    print("TESTING CANTONAL ÖREB SERVICES")
    print(f"{'='*80}")
    print(f"Testing {len(CANTONAL_OEREB_SERVICES)} cantonal ÖREB services...")
    print("This will test XML, PDF, and JSON endpoints for each canton.")
    print(f"{'='*80}")

    results = []
    tested_urls = []

    timeout = 30.0

    for canton, base_url in CANTONAL_OEREB_SERVICES.items():
        print(f"\n--- Testing Canton: {canton} ---")
        print(f"Base URL: {base_url}")

        # Get test coordinates for this canton
        if canton in TEST_COORDINATES:
            x, y = TEST_COORDINATES[canton]
            print(f"Test coordinates: {x}, {y}")
        else:
            print(f"⚠️  No test coordinates available for {canton}")
            results.append({
                'canton': canton,
                'base_url': base_url,
                'egrid_lookup': 'NO_COORDINATES',
                'xml': 'SKIPPED',
                'pdf': 'SKIPPED',
                'json': 'SKIPPED',
                'xml_url': 'N/A',
                'pdf_url': 'N/A',
                'json_url': 'N/A'
            })
            continue

        # Step 1: Get EGRID - use known working EGRID if available, otherwise lookup from coordinates
        egrid = None

        # Check if we have a known working EGRID for this canton (from shared config)
        if canton in KNOWN_WORKING_EGRIDS:
            egrid = KNOWN_WORKING_EGRIDS[canton]
            print(f"✅ Using known working EGRID from shared config: {egrid}")
        else:
            # Try to get EGRID from coordinates
            try:
                async with httpx.AsyncClient(timeout=timeout) as client:
                    egrid_url = "https://api3.geo.admin.ch/rest/services/api/MapServer/identify"
                    params = {
                        'geometry': f'{x},{y}',
                        'geometryType': 'esriGeometryPoint',
                        'layers': 'all:ch.swisstopo-vd.amtliche-vermessung',
                        'mapExtent': f'{x-1000},{y-1000},{x+1000},{y+1000}',
                        'imageDisplay': '1,1,96',
                        'tolerance': '10',
                        'returnGeometry': 'false',
                        'sr': '2056',
                        'f': 'json'
                    }

                    response = await client.get(egrid_url, params=params)
                    response.raise_for_status()
                    data = response.json()

                    if data.get('results') and len(data['results']) > 0:
                        egrid = data['results'][0]['attributes'].get('egris_egrid')
                        print(f"✅ EGRID found from coordinates: {egrid}")
                    else:
                        print("❌ No EGRID found for coordinates")

            except Exception as e:
                print(f"❌ EGRID lookup failed: {str(e)}")

        if not egrid:
            results.append({
                'canton': canton,
                'base_url': base_url,
                'egrid_lookup': 'FAILED',
                'xml': 'NO_EGRID',
                'pdf': 'NO_EGRID',
                'json': 'NO_EGRID',
                'xml_url': 'N/A',
                'pdf_url': 'N/A',
                'json_url': 'N/A'
            })
            continue

        # Step 2: Test XML, PDF, JSON, and URL endpoints
        import time
        timestamp = str(int(time.time() * 1000))  # For BL canton _dc parameter

        # Check if canton has special endpoint patterns
        if canton in SPECIAL_ENDPOINTS:
            special = SPECIAL_ENDPOINTS[canton]
            endpoints = {}

            # Use special endpoints for all formats
            for format_type in ['xml', 'pdf', 'json', 'url']:
                if format_type in special:
                    endpoints[format_type] = special[format_type].format(egrid=egrid, timestamp=timestamp)
                else:
                    # Fallback to standard pattern if not specified
                    endpoints[format_type] = f"{base_url}/extract/{format_type}/?EGRID={egrid}&LANG=de" + ("&WITHIMAGES=true" if format_type == 'xml' else "")
        else:
            # Standard endpoints
            endpoints = {
                'xml': f"{base_url}/extract/xml/?EGRID={egrid}&LANG=de&WITHIMAGES=true",
                'pdf': f"{base_url}/extract/pdf/?EGRID={egrid}&LANG=de",
                'json': f"{base_url}/extract/json/?EGRID={egrid}&LANG=de",
                'url': f"{base_url}/extract/url/?EGRID={egrid}&LANG=de"
            }

        endpoint_results = {}

        for format_type, url in endpoints.items():
            tested_urls.append(url)
            print(f"Testing {format_type.upper()}: {url}")

            try:
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(url)

                    if response.status_code == 200:
                        content_length = len(response.content)
                        print(f"✅ {format_type.upper()}: SUCCESS (Status: {response.status_code}, Size: {content_length} bytes)")
                        endpoint_results[format_type] = 'SUCCESS'
                    else:
                        print(f"❌ {format_type.upper()}: HTTP {response.status_code}")
                        endpoint_results[format_type] = f'HTTP_{response.status_code}'

            except httpx.TimeoutException:
                print(f"⏱️  {format_type.upper()}: TIMEOUT")
                endpoint_results[format_type] = 'TIMEOUT'
            except Exception as e:
                print(f"❌ {format_type.upper()}: ERROR - {str(e)}")
                endpoint_results[format_type] = 'ERROR'

        results.append({
            'canton': canton,
            'base_url': base_url,
            'egrid_lookup': 'SUCCESS' if egrid else 'FAILED',
            'egrid': egrid,
            'xml': endpoint_results.get('xml', 'ERROR'),
            'pdf': endpoint_results.get('pdf', 'ERROR'),
            'json': endpoint_results.get('json', 'ERROR'),
            'url': endpoint_results.get('url', 'ERROR'),
            'xml_url': endpoints['xml'],
            'pdf_url': endpoints['pdf'],
            'json_url': endpoints['json'],
            'url_url': endpoints['url']
        })

    return results, tested_urls


def print_results_table(results: List[Dict], tested_urls: List[str]):
    """Print a formatted table of test results."""

    print(f"\n{'='*120}")
    print("CANTONAL ÖREB SERVICES TEST RESULTS")
    print(f"{'='*120}")

    # Header
    header = f"{'Canton':<6} {'EGRID':<8} {'XML':<8} {'PDF':<8} {'JSON':<8} {'URL':<8} {'Base URL':<40}"
    print(header)
    print("-" * 130)

    # Results
    working_cantons = []
    failed_cantons = []

    for result in results:
        canton = result['canton']
        egrid_status = result['egrid_lookup']
        xml_status = result['xml']
        pdf_status = result['pdf']
        json_status = result['json']
        url_status = result['url']
        base_url = result['base_url']

        # Determine overall status
        success_count = sum(1 for status in [xml_status, pdf_status, json_status, url_status] if status == 'SUCCESS')
        if success_count >= 3:
            working_cantons.append(canton)
            status_symbol = "✅"
        elif success_count >= 1:
            working_cantons.append(canton)
            status_symbol = "⚠️ "
        else:
            failed_cantons.append(canton)
            status_symbol = "❌"

        # Format status strings
        xml_display = xml_status if len(xml_status) <= 6 else xml_status[:6]
        pdf_display = pdf_status if len(pdf_status) <= 6 else pdf_status[:6]
        json_display = json_status if len(json_status) <= 6 else json_status[:6]
        url_display = url_status if len(url_status) <= 6 else url_status[:6]
        base_url_display = base_url if len(base_url) <= 38 else base_url[:35] + "..."

        row = f"{status_symbol} {canton:<4} {egrid_status:<8} {xml_display:<8} {pdf_display:<8} {json_display:<8} {url_display:<8} {base_url_display:<40}"
        print(row)

    # Summary
    print(f"\n{'='*120}")
    print("SUMMARY")
    print(f"{'='*120}")
    print(f"Total cantons tested: {len(results)}")
    print(f"Working cantons: {len(working_cantons)} ({', '.join(sorted(working_cantons))})")
    print(f"Failed cantons: {len(failed_cantons)} ({', '.join(sorted(failed_cantons))})")

    # Detailed URL list
    print(f"\n{'='*120}")
    print("ALL TESTED URLs")
    print(f"{'='*120}")
    for i, url in enumerate(tested_urls, 1):
        print(f"{i:3d}. {url}")





async def main():
    """Main function to run the tests."""
    try:
        results, tested_urls = await test_cantonal_oereb_services()
        print_results_table(results, tested_urls)

        # Working examples
        print(f"\n{'='*120}")
        print("WORKING EXAMPLES")
        print(f"{'='*120}")

        for result in results:
            if result['xml'] == 'SUCCESS' or result['pdf'] == 'SUCCESS' or result['json'] == 'SUCCESS' or result['url'] == 'SUCCESS':
                canton = result['canton']
                egrid = result.get('egrid', 'N/A')
                print(f"\nCanton {canton} (EGRID: {egrid}):")

                if result['xml'] == 'SUCCESS':
                    print(f"  XML:  {result['xml_url']}")
                if result['pdf'] == 'SUCCESS':
                    print(f"  PDF:  {result['pdf_url']}")
                if result['json'] == 'SUCCESS':
                    print(f"  JSON: {result['json_url']}")
                if result['url'] == 'SUCCESS':
                    print(f"  URL:  {result['url_url']}")

        return True

    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
