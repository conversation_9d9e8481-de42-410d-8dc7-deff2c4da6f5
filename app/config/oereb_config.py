"""
ÖREB (Öffentlich-rechtliche Eigentumsbeschränkungen) service configuration.

This module contains the centralized configuration for all Swiss cantonal ÖREB services,
including base URLs and special endpoint patterns. This configuration is shared between
the main application and test files to avoid code duplication.
"""

# Cantonal ÖREB webservice URLs (base URLs without /extract/xml/)
# ALL 26 Swiss cantons have working XML endpoints with correct EGRIDs (tested 2025-05-28)
CANTONAL_OEREB_SERVICES = {
    'AG': 'https://api.geo.ag.ch/v2/oereb',
    'AI': 'https://oereb.ai.ch/ktai/wsgi/oereb',
    'AR': 'https://oereb.ar.ch/ktar/wsgi/oereb',
    'BE': 'https://www.oereb2.apps.be.ch',
    'BL': 'https://oereb.geo.bl.ch',
    'BS': 'https://api.oereb.bs.ch',
    'FR': 'https://geo.fr.ch/RDPPF_ws/RdppfSVC.svc',
    'GE': 'https://ge.ch/terecadastrews/RdppfSVC.svc',
    'GL': 'https://map.geo.gl.ch/oereb',
    'GR': 'https://oereb.geo.gr.ch/oereb',
    'JU': 'https://geo.jura.ch/crdppf_server',
    'LU': 'https://svc.geo.lu.ch/oereb',
    'NE': 'https://sitn.ne.ch/crdppf',
    'NW': 'https://oereb.gis-daten.ch/oereb',
    'OW': 'https://oereb.gis-daten.ch/oereb',
    'SG': 'https://oereb.geo.sg.ch/ktsg/wsgi/oereb',
    'SH': 'https://oereb.geo.sh.ch',
    'SO': 'https://geo.so.ch/api/oereb',
    'SZ': 'https://map.geo.sz.ch/oereb',
    'TG': 'https://map.geo.tg.ch/services/oereb',
    'TI': 'https://map.geo.ti.ch/oereb2',
    'UR': 'https://prozessor-oereb.ur.ch/oereb',
    'VD': 'https://www.rdppf.vd.ch/ws/RdppfSVC.svc',
    'VS': 'https://rdppfvs.geopol.ch',
    'ZG': 'https://oereb.zg.ch/ors',
    'ZH': 'https://maps.zh.ch/oereb/v2'
}

# Special endpoint patterns for cantons that REQUIRE different parameters
# Optimized to include only cantons that cannot use standard base URL patterns (tested 2025-05-28)
# Only 6 cantons need special endpoints: BS, GE, JU, LU, TI, VD (+ AG for consistency)
SPECIAL_ENDPOINTS = {
    # 'AG': {
    #     'xml': 'https://api.geo.ag.ch/v2/oereb/extract/xml/?EGRID={egrid}&LANG=de&WITHIMAGES=true',
    #     'pdf': 'https://api.geo.ag.ch/v2/oereb/extract/pdf/?EGRID={egrid}',
    #     'json': 'https://api.geo.ag.ch/v2/oereb/extract/json/?EGRID={egrid}&LANG=de&WITHIMAGES=true',
    #     'url': 'https://api.geo.ag.ch/v2/oereb/extract/url/?EGRID={egrid}'
    # },
    'BS': {
        'xml': 'https://api.oereb.bs.ch/extract/xml/?EGRID={egrid}&extended',
        'pdf': 'https://api.oereb.bs.ch/extract/pdf/?EGRID={egrid}&extended',
        'json': 'https://api.oereb.bs.ch/extract/json/?EGRID={egrid}&extended',
        'url': 'https://api.oereb.bs.ch/extract/url/?EGRID={egrid}&extended'
    },
    # 'GE': {
    #     'xml': 'https://ge.ch/terecadastrews/RdppfSVC.svc/extract/xml/?EGRID={egrid}&WITHIMAGES=true',
    #     'pdf': 'https://ge.ch/terecadastrews/RdppfSVC.svc/extract/pdf/?EGRID={egrid}',
    #     'json': 'https://ge.ch/terecadastrews/RdppfSVC.svc/extract/json/?EGRID={egrid}&WITHIMAGES=true',
    #     'url': 'https://ge.ch/terecadastrews/RdppfSVC.svc/extract/url/?EGRID={egrid}'
    # },
    'JU': {
        'xml': 'https://geo.jura.ch/crdppf_server/extract/xml?EGRID={egrid}',
        'pdf': 'https://geo.jura.ch/crdppf_server/extract/pdf?EGRID={egrid}',
        'json': 'https://geo.jura.ch/crdppf_server/extract/json?EGRID={egrid}',
        'url': 'https://geo.jura.ch/crdppf_server/extract/url?EGRID={egrid}'
    },
    'LU': {
        'xml': 'https://svc.geo.lu.ch/oereb/extract/xml/?EGRID={egrid}&APP=true',
        'pdf': 'https://svc.geo.lu.ch/oereb/extract/pdf/?EGRID={egrid}&APP=true',
        'json': 'https://svc.geo.lu.ch/oereb/extract/json/?EGRID={egrid}&APP=true',
        'url': 'https://svc.geo.lu.ch/oereb/extract/url/?EGRID={egrid}&APP=true'
    },
    'TI': {
        'xml': 'https://dmz.geo.ti.ch/oereb2/extract/xml/?EGRID={egrid}&WITHIMAGES=true',
        'pdf': 'https://dmz.geo.ti.ch/oereb2/extract/pdf/?EGRID={egrid}',
        'json': 'https://dmz.geo.ti.ch/oereb2/extract/json/?EGRID={egrid}&WITHIMAGES=true',
        'url': 'https://dmz.geo.ti.ch/oereb2/extract/url/?EGRID={egrid}'
    },
    'VD': {
 #       'xml': 'https://www.rdppf.vd.ch/handlers/GetStaticExtract.ashx?origin=simple&method=download&email=&egrids={egrid}&format=xml',
 #       'pdf': 'https://www.rdppf.vd.ch/handlers/GetStaticExtract.ashx?origin=simple&method=download&email=&egrids={egrid}&format=pdf',
 #       'json': 'https://www.rdppf.vd.ch/handlers/GetStaticExtract.ashx?origin=simple&method=download&email=&egrids={egrid}&format=json',
 #       'url': 'https://www.rdppf.vd.ch/handlers/GetStaticExtract.ashx?origin=simple&method=download&email=&egrids={egrid}&format=url'
        'xml': 'https://www.rdppf.vd.ch/ws/RdppfSVC.svc/extract/xml/?EGRID={egrid}',
        'pdf': 'https://www.rdppf.vd.ch/ws/RdppfSVC.svc/extract/pdf/?EGRID={egrid}',
        'json': 'https://www.rdppf.vd.ch/ws/RdppfSVC.svc/extract/json/?EGRID={egrid}',
        'url': 'https://www.rdppf.vd.ch/ws/RdppfSVC.svc/extract/url/?EGRID={egrid}'
    },
}
